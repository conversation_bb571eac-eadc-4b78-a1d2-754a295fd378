---
inclusion: always
type: "always_apply"
---
# AI Assistant Feedback and Testing Guidelines

## Interactive Feedback Protocol

### When to Request Feedback
- Before completing any significant user request
- When clarification is needed on requirements or approach
- After implementing complex features or making substantial changes
- When multiple implementation approaches are possible

### Feedback Process
1. Use the `mcp_interactive_feedback_mcp_interactive_feedback` tool before task completion
2. Provide a clear, one-line summary of the changes or work performed
3. Include the full project directory path
4. If feedback is empty, proceed to complete the request
5. Do not call feedback in a loop - one call per completion cycle

## Documentation and Context Requirements

### Context7 Integration
- Always utilize Context7 MCP for accessing up-to-date library documentation
- Resolve library IDs using `mcp_context7_mcp_resolve_library_id` before fetching docs
- Focus documentation queries on relevant topics (e.g., 'hooks', 'routing', 'authentication')


## Code Quality Standards

### Implementation Approach
- Prioritize minimal, focused implementations
- Avoid over-engineering or unnecessary complexity
- Ensure code is immediately runnable by the user
- Follow established patterns in the existing codebase

### Error Handling
- Always include proper error handling in implementations
- Log meaningful error messages
- Provide graceful fallbacks where appropriate
