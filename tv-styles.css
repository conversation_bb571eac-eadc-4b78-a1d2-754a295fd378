* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
}

.main-container {
    display: flex;
    height: 100vh;
    background: white;
}

/* 左栏 - 专家会客室 */
.expert-sidebar {
    width: 320px;
    background: white;
    border-right: 2px solid #e0e0e0;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 25px 20px;
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    text-align: center;
}

.sidebar-header h2 {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 8px;
}

.sidebar-header p {
    font-size: 16px;
    opacity: 0.9;
}

.expert-list {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.expert-item {
    display: flex;
    align-items: center;
    padding: 18px;
    margin-bottom: 15px;
    border: 2px solid #f0f0f0;
    border-radius: 12px;
    background: #fafafa;
    cursor: pointer;
    transition: all 0.3s ease;
}

.expert-item:hover {
    border-color: #4CAF50;
    background: #f0f8f0;
    transform: translateX(5px);
}

.expert-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid #e0e0e0;
    margin-right: 15px;
    object-fit: cover;
}

.expert-info {
    flex: 1;
}

.expert-name {
    font-size: 18px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 5px;
}

.expert-status {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: var(--theme-main-color, #4CAF50);
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--theme-main-color, #4CAF50);
    border-radius: 50%;
    margin-right: 6px;
    animation: pulse 2s infinite;
}

.call-button {
    width: 50px;
    height: 50px;
    background: var(--theme-main-color, #4CAF50);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.call-button:hover {
    background: var(--theme-dark-color, #45a049);
    transform: scale(1.1);
}

/* 右栏 - 主题交互与聊天区 */
.theme-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #f8f9fa;
}

/* 顶部主题标签栏 */
.theme-header {
    padding: 20px;
    background: white;
    border-bottom: 2px solid #e0e0e0;
}

.theme-tabs {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.theme-tab {
    padding: 12px 25px;
    background: #f0f0f0;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 18px;
    font-weight: 600;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 100px;
    text-align: center;
}

.theme-tab.active {
    background: var(--theme-main-color, #4CAF50);
    color: white;
    border-color: var(--theme-main-color, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.theme-tab:hover:not(.active) {
    background: #e8e8e8;
    border-color: #bbb;
}

/* 磁贴交互区域 */
.content-area {
    flex: 1;
    padding: 25px;
    overflow-y: auto;
    position: relative;
    display: flex;
    flex-direction: column;
}

/* 注释掉可能冲突的样式，由 theme-styles.css 处理
.tile-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}
*/

/* 注释掉可能冲突的样式，由 theme-styles.css 处理
.tile-item {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 120px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.tile-item:hover {
    border-color: #4CAF50;
    background: #f0f8f0;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}
*/

/* 注释掉可能冲突的样式，由 theme-styles.css 处理
.tile-icon {
    font-size: 36px;
    margin-bottom: 10px;
}
*/

/* 注释掉可能冲突的样式，由 theme-styles.css 处理
.tile-title {
    font-size: 16px;
    font-weight: 600;
    color: #2d3748;
    line-height: 1.3;
}

.tile-desc {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
    line-height: 1.2;
}
*/

/* 返回按钮 */
.back-button {
    display: none;
    padding: 12px 25px;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    cursor: pointer;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.back-button:hover {
    background: #1976D2;
    transform: translateY(-2px);
}

/* 聊天界面区域 (结构与 index.html 右侧聊天区域一致) */
.chat-area {
    flex: 1;
    display: none; /* 初始隐藏 */
    flex-direction: column;
    background: white;
}

#chat-area {
  display: none;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background: #fff;
  box-shadow: 0 0 20px #ccc;
  border-radius: 12px;
  padding: 16px;
  overflow: hidden;
}

#messages-container {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 12px;
}

#user-input {
  width: 100%;
  min-height: 40px;
  border-radius: 8px;
  border: 1px solid #ccc;
  padding: 8px;
  font-size: 16px;
  resize: none;
}

.send-btn {
  margin-top: 8px;
  align-self: flex-end;
  background: #7C3AED;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 24px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.2s;
}

.send-btn:hover {
  background: #5B21B6;
}

.send-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

/* 消息气泡样式 (来自 index.html) */
.message-item {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    flex-shrink: 0;
    border: 2px solid #f1f5f9;
    object-fit: cover;
}

.message-content {
    padding: 15px;
    border-radius: 12px;
    position: relative;
    flex: 1;
    min-width: 0;
    word-wrap: break-word;
    word-break: break-all;
    overflow-wrap: break-word;
    font-size: 16px;
    line-height: 1.5;
}

.message-user {
    margin-left: auto;
    flex-direction: row-reverse;
    max-width: 33.33%;
}

.message-user .message-content {
    background: #3b82f6;
    color: white;
    text-align: right;
    border-top-right-radius: 0;
}

.message-user .message-avatar {
    border-color: #3b82f6;
}

.message-ai {
    max-width: 66.67%;
}

.message-ai .message-content {
    background: #f1f5f9;
    color: #1e293b;
    border-top-left-radius: 0;
}

.message-ai .message-avatar {
    border-color: #f1f5f9;
}

/* Markdown 内容样式 */
.message-ai .markdown-body {
    background: transparent;
    padding: 0;
    margin: 0;
    box-shadow: none;
}

/* 加载指示器 */
.loading-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    color: #6b7280;
    font-style: italic;
}

.loading-dots::after {
    content: '';
    animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* 动画效果 */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes wave {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.calling-animation {
    animation: wave 2s infinite;
}

/* 通话界面 */
.call-interface {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.call-content {
    text-align: center;
    color: white;
}

.call-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid white;
    margin: 0 auto 20px;
    object-fit: cover;
}

.call-name {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
}

.call-status {
    font-size: 18px;
    margin-bottom: 30px;
    opacity: 0.8;
}

.call-controls {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.end-call-btn {
    width: 60px;
    height: 60px;
    background: #f44336;
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.end-call-btn:hover {
    background: #d32f2f;
    transform: scale(1.1);
}