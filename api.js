// api.js - 基于Coze官方文档的会话创建和对话实现

// 创建会话
async function createConversation(botId, metadata = {}) {
    const url = 'https://api.coze.cn/v1/conversation/create';
    const headers = {
        'Authorization': `Bearer ${COZE_CONFIG.token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    };

    const payload = {
        bot_id: botId,
        meta_data: metadata,
        connector_id: 1024 // API渠道
    };

    try {
        console.log('创建会话请求:', { botId, metadata });
        
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('创建会话响应:', data);

        if (data.code === 0 && data.data) {
            return {
                success: true,
                conversationId: data.data.id,
                lastSectionId: data.data.last_section_id
            };
        } else {
            return {
                success: false,
                error: data.msg || '创建会话失败'
            };
        }

    } catch (error) {
        console.error('创建会话失败:', error);
        return {
            success: false,
            error: error.message || '网络错误'
        };
    }
}

// 在会话中发起对话
async function chatInConversation(conversationId, botId, userMessage) {
    const url = 'https://api.coze.cn/v3/chat';
    const headers = {
        'Authorization': `Bearer ${COZE_CONFIG.token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    };

    const payload = {
        bot_id: botId,
        user_id: `user_${Date.now()}`,
        conversation_id: conversationId,
        additional_messages: [{
            role: 'user',
            content: JSON.stringify([{
                type: 'text',
                text: userMessage
            }]),
            content_type: 'object_string'
        }],
        stream: false
    };

    try {
        console.log('发起对话请求:', { conversationId, botId, userMessage });
        
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('发起对话响应:', data);

        if (data.code === 0 && data.data && data.data.messages) {
            // 找到助手的回复
            const assistantMessages = data.data.messages.filter(msg => 
                msg.role === 'assistant' && msg.type === 'answer'
            );
            
            if (assistantMessages.length > 0) {
                const lastMessage = assistantMessages[assistantMessages.length - 1];
                return {
                    success: true,
                    response: lastMessage.content,
                    conversationId: conversationId
                };
            }
        }

        return {
            success: false,
            error: '未能获取有效的回复内容'
        };

    } catch (error) {
        console.error('发起对话失败:', error);
        return {
            success: false,
            error: error.message || '网络错误'
        };
    }
}

// 简化的对话流程：直接发起对话（不需要预创建会话）
async function sendDirectChat(botId, userMessage) {
    const url = 'https://api.coze.cn/v3/chat';
    const headers = {
        'Authorization': `Bearer ${COZE_CONFIG.token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    };

    const payload = {
        bot_id: botId,
        user_id: `user_${Date.now()}`,
        query: userMessage,
        stream: false
    };

    try {
        console.log('直接发起对话请求:', { botId, userMessage });
        
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('直接对话响应:', data);

        if (data.code === 0 && data.data && data.data.messages) {
            // 找到助手的回复
            const assistantMessages = data.data.messages.filter(msg => 
                msg.role === 'assistant' && msg.type === 'answer'
            );
            
            if (assistantMessages.length > 0) {
                const lastMessage = assistantMessages[assistantMessages.length - 1];
                return {
                    success: true,
                    response: lastMessage.content
                };
            }
        }

        return {
            success: false,
            error: '未能获取有效的回复内容'
        };

    } catch (error) {
        console.error('直接对话失败:', error);
        
        // 根据错误类型提供友好的错误信息
        let errorMessage = '抱歉，服务暂时不可用，请稍后再试。';
        
        if (error.message.includes('401')) {
            errorMessage = '认证失败：请检查API Token是否正确。';
        } else if (error.message.includes('403')) {
            errorMessage = '权限不足：请检查Token权限或Bot ID是否正确。';
        } else if (error.message.includes('429')) {
            errorMessage = '请求过于频繁：请稍后再试。';
        } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
            errorMessage = '网络连接错误：请检查网络连接后重试。';
        }

        return {
            success: false,
            error: errorMessage
        };
    }
}

// 主函数：发送消息（使用简化的直接对话方式）
async function sendMessage(prompt, expert) {
    if (!expert || !expert.bot_id) {
        return {
            success: false,
            error: '专家配置错误：缺少Bot ID'
        };
    }

    if (!COZE_CONFIG.token) {
        return {
            success: false,
            error: 'API配置错误：缺少访问令牌'
        };
    }

    return await sendDirectChat(expert.bot_id, prompt);
}

// 流式对话方法 - 自动创建会话或续会话
async function cozeChatStream({ botId, userId, prompt, conversationId, onDelta, onComplete, onError }) {
    try {
        const url = 'https://api.coze.cn/v3/chat';
        const headers = {
            'Authorization': `Bearer ${COZE_CONFIG.token}`,
            'Content-Type': 'application/json'
        };
        
        const body = {
            bot_id: botId,
            user_id: userId,
            stream: true,
            auto_save_history: false,
            additional_messages: [
                { role: 'user', content: prompt }
            ]
        };
        
        // 如果有会话ID，则带上它
        if (conversationId) {
            body.conversation_id = conversationId;
        }

        console.log('发起流式对话请求:', { botId, userId, prompt, conversationId });
        
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(body)
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        if (!response.body) {
            throw new Error('无流式响应');
        }

        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';
        let newConversationId = conversationId;

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                buffer += decoder.decode(value, { stream: true });
                const lines = buffer.split('\n');
                buffer = lines.pop() || '';

                for (const line of lines) {
                    const trimmedLine = line.trim();
                    if (!trimmedLine) continue;

                    if (trimmedLine.startsWith('data:')) {
                        try {
                            const eventData = JSON.parse(trimmedLine.slice(5));
                            console.log('流式数据:', eventData);

                            // 处理流式内容
                            if (eventData.event === 'conversation.message.delta' && eventData.data && eventData.data.content) {
                                onDelta && onDelta(eventData.data.content);
                            }

                            // 记录新的会话ID
                            if (eventData.data && eventData.data.conversation_id) {
                                newConversationId = eventData.data.conversation_id;
                            }

                            // 处理完成事件
                            if (eventData.event === 'conversation.message.completed') {
                                onComplete && onComplete(newConversationId);
                                return;
                            }
                        } catch (parseError) {
                            console.warn('解析流式数据失败:', parseError);
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }
    } catch (error) {
        console.error('流式对话失败:', error);
        onError && onError(error.message);
    }
}

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { 
        sendMessage, 
        createConversation, 
        chatInConversation, 
        sendDirectChat,
        cozeChatStream 
    };
}
