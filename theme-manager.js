// 主题管理类
class ThemeManager {
    constructor() {
        this.currentTheme = 'yangsheng'; // 默认养生主题
        this.themeColors = {
            sannong: '#059669', // 深绿色，低饱和度
            yangsheng: '#7C3AED', // 深紫色，低饱和度
            dangjian: '#DC2626', // 深红色，低饱和度
            jianshen: '#2563EB', // 深蓝色，低饱和度
            yanghua: '#EA580C' // 深橙色，低饱和度
        };
        // 会话管理
        this.conversations = {}; // 主题 -> 会话ID
        this.currentConversationId = null; // 当前会话ID
    }

    // 设置主题颜色
    setThemeColor(color) {
        console.log('设置主题颜色:', color);
        document.documentElement.style.setProperty('--theme-main-color', color);
        
        // 生成浅色和深色变体
        const lightColor = this.adjustBrightness(color, 1.3);
        const darkColor = this.adjustBrightness(color, 0.7);
        
        document.documentElement.style.setProperty('--theme-light-color', lightColor);
        document.documentElement.style.setProperty('--theme-dark-color', darkColor);
        
        console.log('CSS变量已设置:');
        console.log('--theme-main-color:', color);
        console.log('--theme-light-color:', lightColor);
        console.log('--theme-dark-color:', darkColor);
    }

    // 亮度调整函数
    adjustBrightness(hex, factor) {
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        
        const newR = Math.min(255, Math.round(r * factor));
        const newG = Math.min(255, Math.round(g * factor));
        const newB = Math.min(255, Math.round(b * factor));
        
        return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
    }

    // 切换主题
    switchTheme(themeKey) {
        if (this.themeColors[themeKey]) {
            this.currentTheme = themeKey;
            this.setThemeColor(this.themeColors[themeKey]);
            this.updateThemeTabs(themeKey);
            this.renderTiles(themeKey);
            console.log(`主题已切换到: ${themeKey}, 颜色: ${this.themeColors[themeKey]}`);
        }
    }
    
    // 根据主题数据设置颜色
    setThemeColorFromData(themeData) {
        if (themeData && themeData.color) {
            this.setThemeColor(themeData.color);
        }
    }

    // 更新主题标签状态
    updateThemeTabs(activeTheme) {
        const tabs = document.querySelectorAll('.theme-tab');
        tabs.forEach(tab => {
            tab.classList.remove('active');
            if (tab.dataset.theme === activeTheme) {
                tab.classList.add('active');
            }
        });
    }

    // 渲染磁贴
    renderTiles(themeKey) {
        let tileData;
        switch(themeKey) {
            case 'yangsheng':
                tileData = YANGSHENG_DATA;
                break;
            case 'sannong':
                tileData = SANNONG_DATA;
                break;
            case 'dangjian':
                tileData = DANGJIAN_DATA;
                break;
            case 'jianshen':
                tileData = JIANSHEN_DATA;
                break;
            case 'yanghua':
                tileData = YANGHUA_DATA;
                break;
            default:
                tileData = YANGSHENG_DATA;
        }
        
        if (!tileData) return;

        // 根据主题数据设置颜色
        this.setThemeColorFromData(tileData);

        // 渲染一级磁贴
        this.renderLevel1Tiles(tileData);
    }

    // 渲染一级磁贴
    renderLevel1Tiles(tileData) {
        const container = document.getElementById('tile-container');
        if (!container) return;

        container.innerHTML = '';
        container.className = 'tile-grid level-1';

        tileData.firstLevel.forEach(tile => {
            const tileElement = this.createTileElement(tile, 'level-1');
            tileElement.addEventListener('click', () => {
                this.onTileClick(tile, tileData);
            });
            container.appendChild(tileElement);
        });
    }

    // 渲染二级磁贴
    renderLevel2Tiles(parentTile, tileData) {
        const container = document.getElementById('tile-container');
        if (!container) return;

        container.innerHTML = '';
        container.className = 'tile-grid level-2';

        // 添加返回按钮
        const backButton = this.createBackButton();
        container.appendChild(backButton);

        const secondLevelData = tileData.secondLevel[parentTile.id];
        if (secondLevelData) {
            secondLevelData.forEach(tile => {
                const tileElement = this.createTileElement(tile, 'level-2');
                tileElement.addEventListener('click', () => {
                    this.onSecondLevelTileClick(tile, parentTile);
                });
                container.appendChild(tileElement);
            });
        }
    }

    // 创建磁贴元素
    createTileElement(tile, level) {
        const tileDiv = document.createElement('div');
        tileDiv.className = `tile ${level}`;
        if (level === 'level-1') {
            tileDiv.innerHTML = `
                <div class="tile-icon">${tile.icon}</div>
                <div class="tile-title">${tile.title}</div>
                <div class="tile-desc">${tile.desc}</div>
            `;
        } else if (level === 'level-2') {
            tileDiv.innerHTML = `
                <div class="tile-icon">${tile.icon || ''}</div>
                <div class="tile-title">${tile.title}</div>
            `;
        } else {
            tileDiv.innerHTML = `
                <div class="tile-title">${tile.title}</div>
            `;
        }
        return tileDiv;
    }

    // 创建返回按钮
    createBackButton() {
        const backButton = document.createElement('div');
        backButton.className = 'tile back-button';
        backButton.innerHTML = `
            <div class="tile-icon">&#8592;</div>
            <div class="tile-title">返回</div>
        `;
        backButton.addEventListener('click', () => {
            this.renderTiles(this.currentTheme);
        });
        return backButton;
    }
    
    // 返回磁贴界面
    goBackToTiles() {
        const tileArea = document.getElementById('tile-content-area');
        const chatArea = document.getElementById('chat-area');
        
        if (tileArea && chatArea) {
            chatArea.style.display = 'none';
            tileArea.style.display = 'block';
            this.renderTiles(this.currentTheme);
        }
    }

    // 一级磁贴点击事件
    onTileClick(tile, tileData) {
        this.renderLevel2Tiles(tile, tileData);
    }

    // 二级磁贴点击事件，先确保会话已创建再发消息
    async onSecondLevelTileClick(tile, parentTile) {
        console.log('=== 二级磁贴点击事件触发 ===');
        console.log('磁贴信息:', tile);
        console.log('父级磁贴:', parentTile);
        console.log('当前主题:', this.currentTheme);
        
        if (!tile.prompt) {
            console.error('错误：磁贴缺少 prompt 字段');
            this.showError('磁贴配置错误：缺少提示词');
            return;
        }
        
        console.log('触发AI问答:', {
            prompt: tile.prompt,
            parentTile: parentTile.title,
            currentTile: tile.title
        });
        
        // 显示聊天窗口并自动发送预置提示词
        await this.showChatWindowAndSendPrompt(tile.prompt);
    }

    // 发送AI请求 - 使用流式对话
    async sendAIRequest(prompt, showLoadingState = true) {
        try {
            if (showLoadingState) {
                this.showLoading();
            }
            
            // 获取当前主题和专家配置
            const themeKey = this.currentTheme;
            const currentExpert = this.getExpertForCurrentTheme();
            
            if (!currentExpert) {
                if (showLoadingState) this.hideLoading();
                this.showError('未找到对应的专家配置');
                return;
            }
            
            // 获取当前会话ID
            const conversationId = this.conversations[themeKey];
            
            // 先添加用户消息到聊天界面
            this.addMessageToChat('user', prompt);
            
            // 添加空的AI消息，用于实时更新
            this.addMessageToChat('assistant', '');
            const aiMessageIndex = this.getLastAIMessageIndex();
            
            // 发起流式对话
            await cozeChatStream({
                botId: currentExpert.bot_id,
                userId: 'user_' + Date.now(),
                prompt: prompt,
                conversationId: conversationId,
                onDelta: (delta) => {
                    // 实时更新AI消息内容
                    this.updateAIMessage(aiMessageIndex, delta);
                },
                onComplete: (newConversationId) => {
                    // 保存新的会话ID
                    this.conversations[themeKey] = newConversationId;
                    this.currentConversationId = newConversationId;
                    if (showLoadingState) {
                        this.hideLoading();
                    }
                },
                onError: (error) => {
                    console.error('流式对话失败:', error);
                    if (showLoadingState) {
                        this.hideLoading();
                    }
                    this.showError('对话失败: ' + error);
                }
            });
            
        } catch (error) {
            console.error('AI请求失败:', error);
            if (showLoadingState) this.hideLoading();
            this.showError('请求失败，请重试');
        }
    }
    
    // 根据主题获取对应的专家配置
    getExpertForCurrentTheme(themeKey = null) {
        const themeExpertMap = {
            'yangsheng': {
                id: 'expert1',
                name: '养生专家',
                bot_id: '7530915278943830051',
                specialty: '中医养生、食疗保健'
            },
            'sannong': {
                id: 'expert2',
                name: '农业专家',
                bot_id: '7530940616785051683',
                specialty: '种植技术、养殖管理'
            },
            'dangjian': {
                id: 'expert3',
                name: '党建专家',
                bot_id: '7528256246898622518',
                specialty: '党史学习、理论宣讲'
            },
            'jianshen': {
                id: 'expert4',
                name: '健身专家',
                bot_id: '7524620947187056679',
                specialty: '运动指导、康复训练'
            },
            'yanghua': {
                id: 'expert5',
                name: '园艺专家',
                bot_id: '7523880023146168366',
                specialty: '花卉种植、园艺技巧'
            }
        };
        
        const targetTheme = themeKey || this.currentTheme;
        return themeExpertMap[targetTheme];
    }

    // 显示加载状态
    showLoading() {
        const container = document.getElementById('tile-container');
        if (container) {
            container.classList.add('loading');
        }
    }

    // 隐藏加载状态
    hideLoading() {
        const container = document.getElementById('tile-container');
        if (container) {
            container.classList.remove('loading');
        }
    }

    // 聊天窗口展示并自动发消息
    async showChatWindowAndSendPrompt(prompt) {
        const tileArea = document.getElementById('tile-content-area');
        const chatArea = document.getElementById('chat-area');
        if (tileArea && chatArea) {
            tileArea.style.display = 'none';
            chatArea.style.display = 'flex';
            const chatAgentName = document.getElementById('chat-agent-name');
            if (chatAgentName) {
                const currentExpert = this.getExpertForCurrentTheme();
                chatAgentName.textContent = currentExpert ? currentExpert.name : 'AI助手';
            }
            // 清空聊天内容（可选）
            // document.getElementById('messages-container').innerHTML = '';
            await this.sendAIRequest(prompt, false);
        }
    }

    // 聊天窗口返回按钮逻辑
    goBackToTiles() {
        const tileArea = document.getElementById('tile-content-area');
        const chatArea = document.getElementById('chat-area');
        if (tileArea && chatArea) {
            chatArea.style.display = 'none';
            tileArea.style.display = 'block';
            // 可选：清空聊天内容
            // document.getElementById('messages-container').innerHTML = '';
        }
    }
    
    // 显示AI回复
    showAIResponse(response) {
        // 添加AI回复到聊天界面
        this.addMessageToChat('assistant', response);
    }
    
    // 添加消息到聊天界面
    addMessageToChat(role, content) {
        const messagesContainer = document.getElementById('messages-container');
        if (!messagesContainer) return;
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${role}-message`;
        
        if (role === 'assistant') {
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="assistant-avatar">
                        <img src="touxiang/11.png" alt="AI助手" />
                    </div>
                    <div class="message-text">${content}</div>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="message-content">
                    <div class="message-text">${content}</div>
                    <div class="user-avatar">
                        <img src="touxiang/3.png" alt="用户" />
                    </div>
                </div>
            `;
        }
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // 显示错误信息
    showError(message) {
        alert(message);
    }
    
    // 处理用户输入发送消息
    async sendMessageFromInput() {
        const userInput = document.getElementById('user-input');
        const prompt = userInput.value.trim();
        
        if (!prompt) return;
        
        // 清空输入框
        userInput.value = '';
        
        // 发送AI请求（显示加载状态）
        await this.sendAIRequest(prompt, true);
    }
    
    // 获取最后一条AI消息的索引
    getLastAIMessageIndex() {
        const messagesContainer = document.getElementById('messages-container');
        const messages = messagesContainer.querySelectorAll('.message');
        for (let i = messages.length - 1; i >= 0; i--) {
            if (messages[i].classList.contains('assistant-message')) {
                return i;
            }
        }
        return -1;
    }
    
    // 更新AI消息内容（用于流式输出）
    updateAIMessage(messageIndex, delta) {
        const messagesContainer = document.getElementById('messages-container');
        const messages = messagesContainer.querySelectorAll('.message');
        
        if (messageIndex >= 0 && messageIndex < messages.length) {
            const aiMessage = messages[messageIndex];
            const messageText = aiMessage.querySelector('.message-text');
            
            if (messageText) {
                // 追加新内容
                messageText.textContent += delta;
                // 滚动到底部
                messagesContainer.scrollTop = messagesContainer.scrollHeight;
            }
        }
    }

    // 初始化主题管理器
    init() {
        // 设置默认主题
        this.switchTheme(this.currentTheme);
        
        // 绑定主题标签事件
        this.bindThemeTabEvents();
    }

    // 绑定主题标签事件
    bindThemeTabEvents() {
        const tabs = document.querySelectorAll('.theme-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const themeKey = tab.dataset.theme;
                if (themeKey) {
                    this.switchTheme(themeKey);
                }
            });
        });
    }
}

// 创建全局主题管理器实例
const themeManager = new ThemeManager();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化，确保其他组件先加载
    setTimeout(() => {
        themeManager.init();
        console.log('主题管理器初始化完成');
    }, 100);
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ThemeManager;
} 