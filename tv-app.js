// 主应用逻辑
class TVApp {
    constructor() {
        this.currentTheme = 'yangsheng';
        this.currentLevel = 1; // 1: 一级磁贴, 2: 二级磁贴
        this.currentFirstLevelTopic = '';
        this.currentExpert = null;
        this.messages = [];
    }

    // 初始化应用
    init() {
        this.initializeExperts();
        this.initializeThemes();
        // loadThemeContent 现在由 theme-manager.js 处理
        this.hideChatInterface();
        console.log('TVApp 初始化完成 - 专家和聊天功能已准备就绪');
    }

    // 初始化专家列表
    initializeExperts() {
        const expertList = document.getElementById('expert-list');
        expertList.innerHTML = '';

        EXPERTS.forEach(expert => {
            const expertItem = document.createElement('div');
            expertItem.className = 'expert-card';
            expertItem.title = `擅长：${expert.specialty || ''}`;
            
            // 头像+在线状态点
            const avatarBox = document.createElement('div');
            avatarBox.style.position = 'relative';
            const avatar = document.createElement('img');
            avatar.className = 'expert-avatar';
            avatar.src = expert.avatar;
            avatar.alt = expert.name;
            
            // 在线状态点
            const statusDot = document.createElement('span');
            statusDot.className = 'status-dot';
            avatarBox.appendChild(avatar);
            avatarBox.appendChild(statusDot);
            
            // 专家信息
            const info = document.createElement('div');
            info.className = 'expert-info';
            const name = document.createElement('div');
            name.className = 'expert-name';
            name.textContent = expert.name;
            const status = document.createElement('div');
            status.className = 'expert-status';
            status.innerHTML = '<span>在线</span>';
            info.appendChild(name);
            info.appendChild(status);
            
            // 电话按钮
            const callBtn = document.createElement('button');
            callBtn.className = 'call-btn';
            callBtn.title = '点击通话';
            callBtn.innerHTML = '<svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 16.92V21a2 2 0 0 1-2.18 2A19.72 19.72 0 0 1 3 5.18 2 2 0 0 1 5 3h4.09a2 2 0 0 1 2 1.72c.13 1.13.37 2.23.72 3.28a2 2 0 0 1-.45 2.11l-1.27 1.27a16 16 0 0 0 6.29 6.29l1.27-1.27a2 2 0 0 1 2.11-.45c1.05.35 2.15.59 3.28.72A2 2 0 0 1 21 18.91V21z"></path></svg>';
            callBtn.onclick = (e) => {
                e.stopPropagation();
                alert('模拟通话：' + expert.name);
            };
            
            // 组装
            expertItem.appendChild(avatarBox);
            expertItem.appendChild(info);
            expertItem.appendChild(callBtn);
            expertItem.onclick = () => this.startChatWithExpert(expert.id);
            expertList.appendChild(expertItem);
        });
    }

    // 初始化主题标签 - 现在由 theme-manager.js 处理
    initializeThemes() {
        // 主题标签初始化现在由 theme-manager.js 统一管理
        console.log('主题标签初始化已由 theme-manager.js 处理');
    }

    // 切换主题 - 现在由 theme-manager.js 处理
    switchTheme(theme) {
        // 主题切换现在由 theme-manager.js 统一管理
        console.log('主题切换已由 theme-manager.js 处理');
    }

    // 加载主题内容 - 现在由 theme-manager.js 处理
    loadThemeContent(theme) {
        // 主题内容现在由 theme-manager.js 统一管理
        // 这里保留方法以避免错误，但实际功能已迁移
        console.log('主题内容加载已由 theme-manager.js 处理');
    }

    // 创建磁贴元素 - 现在由 theme-manager.js 处理
    createTileElement(tile, level) {
        // 磁贴元素创建现在由 theme-manager.js 统一管理
        console.log('磁贴元素创建已由 theme-manager.js 处理');
        return document.createElement('div'); // 返回空元素避免错误
    }

    // 处理一级磁贴点击 - 现在由 theme-manager.js 处理
    handleFirstLevelClick(topicId) {
        // 一级磁贴点击现在由 theme-manager.js 统一管理
        console.log('一级磁贴点击已由 theme-manager.js 处理');
    }

    // 处理二级磁贴点击 - 现在由 theme-manager.js 处理
    async handleSecondLevelClick(prompt) {
        // 二级磁贴点击现在由 theme-manager.js 统一管理
        console.log('二级磁贴点击已由 theme-manager.js 处理');
    }

    // 返回一级磁贴 - 现在由 theme-manager.js 处理
    goBackToFirstLevel() {
        // 返回功能现在由 theme-manager.js 统一管理
        console.log('返回功能已由 theme-manager.js 处理');
    }

    // 开始与专家聊天
    startChatWithExpert(expertId) {
        this.currentExpert = EXPERTS.find(e => e.id === expertId);
        this.showChatInterface();
    }

    // 显示聊天界面
    showChatInterface() {
        document.getElementById('theme-header').style.display = 'none';
        document.getElementById('tile-content-area').style.display = 'none';
        document.getElementById('chat-area').style.display = 'flex';
        if (this.currentExpert) {
            document.getElementById('chat-agent-name').textContent = this.currentExpert.name;
        }
    }

    // 隐藏聊天界面
    hideChatInterface() {
        document.getElementById('theme-header').style.display = 'block';
        document.getElementById('tile-content-area').style.display = 'flex';
        document.getElementById('chat-area').style.display = 'none';
    }

    // 添加消息到界面
    addMessage(sender, text) {
        this.messages.push({ sender, text });
        this.renderMessages();
    }

    // 渲染消息
    renderMessages() {
        const messagesContainer = document.getElementById('messages-container');
        messagesContainer.innerHTML = '';
        this.messages.forEach(msg => {
            const messageElement = document.createElement('div');
            messageElement.className = `message-item message-${msg.sender}`;
            messageElement.innerHTML = `
                <img src="${msg.sender === 'user' ? 'touxiang/3.png' : this.currentExpert.avatar}" class="message-avatar" alt="avatar">
                <div class="message-content">${marked.parse(msg.text)}</div>
            `;
            messagesContainer.appendChild(messageElement);
        });
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // 从输入框发送消息
    async sendMessageFromInput() {
        const userInput = document.getElementById('user-input');
        const prompt = userInput.value.trim();
        if (!prompt) return;

        this.addMessage('user', prompt);
        userInput.value = '';

        try {
            const response = await sendMessage(prompt, this.currentExpert);
            console.log('收到API响应:', response);
            
            let aiMessage = '抱歉，未能获取到有效回复。';
            
            // 使用新的API响应格式（基于main.js的正确实现）
            if (response.success) {
                aiMessage = response.response;
            } else {
                aiMessage = response.error || '抱歉，未能获取到有效回复。';
            }
            
            this.addMessage('ai', aiMessage);
        } catch (error) {
            console.error('Error sending message:', error);
            this.addMessage('ai', `抱歉，我暂时无法回答您的问题。错误: ${error.message}`);
        }
    }
}

// 搜索框事件 - 保留基本功能
const searchInput = document.getElementById('expert-search-input');
if (searchInput) {
    searchInput.addEventListener('input', e => {
        // 这里可以实现搜索过滤功能
        console.log('搜索:', e.target.value);
    });
}

// 创建全局应用实例 - 主要用于聊天功能
const tvApp = new TVApp();

// 全局函数 - 部分功能已迁移到 theme-manager.js
function goBackToFirstLevel() {
    // 返回功能现在由 theme-manager.js 处理
    console.log('返回功能已由 theme-manager.js 处理');
}

function sendMessageFromInput() {
    tvApp.sendMessageFromInput();
}

function handleKeyDown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        // 调用 themeManager 的发送方法
        if (typeof themeManager !== 'undefined' && themeManager.sendMessageFromInput) {
            themeManager.sendMessageFromInput();
        } else {
            // 备用方案：调用 tvApp 的方法
            tvApp.sendMessageFromInput();
        }
    }
}

// 页面加载完成后初始化 - 现在由 theme-manager.js 统一管理
document.addEventListener('DOMContentLoaded', function() {
    // 初始化由 theme-manager.js 统一管理
    console.log('页面初始化已由 theme-manager.js 处理');
    // 但仍然需要初始化 TVApp 的专家和聊天功能
    tvApp.init();
});
