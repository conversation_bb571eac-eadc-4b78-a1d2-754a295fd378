FROM nginx:alpine

# 复制项目文件到nginx默认目录
COPY . /usr/share/nginx/html/

# 复制nginx配置文件
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 设置文件权限
RUN chmod -R 644 /usr/share/nginx/html/*.html \
    && chmod -R 644 /usr/share/nginx/html/*.js \
    && chmod -R 644 /usr/share/nginx/html/*.css \
    && chmod -R 644 /usr/share/nginx/html/*.json \
    && chmod -R 755 /usr/share/nginx/html/touxiang/

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]