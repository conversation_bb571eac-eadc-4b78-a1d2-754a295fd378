/* 
 * 低饱和度主题样式 - 版本 2.0
 * 更新时间: 2025-01-27
 * 设计理念: 参考现代设计系统，采用低饱和度配色方案
 */

/* 主题主色变量 - 低饱和度版本 */
:root {
    --theme-main-color: #6B7280; /* 默认灰色系 */
    --theme-light-color: #9CA3AF;
    --theme-dark-color: #4B5563;
}

/* 磁贴网格布局：每行3个，两行，均匀分布 */
.tile-grid,
.tile-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 48px;
    padding: 48px 32px 0 32px;
    justify-items: stretch;
    align-items: stretch;
    max-width: 1400px;
    margin: 0 auto;
    min-height: 600px;
    max-height: 900px;
    overflow-y: auto;
}

/* 磁贴基础样式 - 低饱和度 */
.tile {
    aspect-ratio: 1 / 1;
    height: auto;
    min-height: 0;
    min-width: 0;
    width: 100%;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
    color: #fff;
    background: var(--theme-main-color);
    border-radius: 16px;
    padding: 0 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    font-size: 32px;
    font-weight: 500;
    text-align: center;
    transition: all 0.2s ease;
    cursor: pointer;
    user-select: none;
    overflow: hidden;
    border: 1px solid rgba(255,255,255,0.1);
}

.tile.level-1, .tile.level-2, .tile.level-3 {
    background: var(--theme-main-color);
    color: #fff;
}

.tile .tile-title, .tile .tile-desc {
    color: #fff;
    font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
}

.tile .tile-title {
    font-size: 32px !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
    word-break: break-all !important;
    display: block !important;
    text-align: center !important;
}

.tile .tile-desc {
    font-size: 18px !important;
    opacity: 0.9 !important;
    font-weight: 400 !important;
    margin-top: 4px !important;
    word-break: break-all !important;
    display: block !important;
    text-align: center !important;
}

.tile .tile-icon {
    font-size: 56px !important;
    margin-bottom: 12px !important;
    opacity: 0.95 !important;
    display: block !important;
    text-align: center !important;
    background: rgba(255,255,255,0.1) !important;
    border: 1px solid rgba(255,255,255,0.2) !important;
    border-radius: 8px !important;
    padding: 8px !important;
    min-height: 80px !important;
    line-height: 80px !important;
}

/* 鼠标移入/点击效果 - 更柔和 */
.tile:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0,0,0,0.12);
    border-color: rgba(255,255,255,0.2);
}
.tile:active {
    transform: translateY(0);
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

/* 返回按钮样式 - 低饱和度 */
.tile.back-button {
    background: var(--theme-main-color);
    color: #fff;
    border-radius: 16px;
    font-size: 24px;
    font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    min-height: 80px;
    min-width: 0;
    width: 160px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid rgba(255,255,255,0.1);
}
.tile.back-button .tile-icon {
    font-size: 28px;
    margin-right: 8px;
    color: #fff;
}

/* 隐藏原有的按钮样式 */
.back-button {
    background: none;
    border: none;
    box-shadow: none;
    padding: 0;
    margin: 0;
    outline: none;
    display: none;
}

@media (max-width: 1200px) {
    .tile-grid, .tile-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 32px;
        padding: 32px 12px 0 12px;
    }
}
@media (max-width: 800px) {
    .tile-grid, .tile-container {
        grid-template-columns: 1fr;
        gap: 18px;
        padding: 12px 4px 0 4px;
    }
    .tile {
        font-size: 22px;
        padding: 24px 6px 12px 6px;
        min-height: 120px;
    }
    .tile .tile-title {
        font-size: 22px;
    }
    .tile .tile-icon {
        font-size: 32px;
    }
    .tile .tile-desc {
        font-size: 14px;
    }
}

/* 主题标签样式 - 低饱和度 */
.theme-tab {
    background: var(--theme-main-color);
    color: #fff;
    border: none;
    border-radius: 12px;
    padding: 12px 20px;
    margin: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 16px;
    font-weight: 500;
    border: 1px solid rgba(255,255,255,0.1);
}

.theme-tab:hover {
    background: var(--theme-light-color);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.12);
}

.theme-tab.active {
    background: var(--theme-dark-color);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: rgba(255,255,255,0.2);
}

/* 主题色彩映射 - 低饱和度配色 */
.theme-colors {
    --sannong-color: #059669; /* 深绿色，低饱和度 */
    --yangsheng-color: #7C3AED; /* 深紫色，低饱和度 */
    --dangjian-color: #DC2626; /* 深红色，低饱和度 */
    --jianshen-color: #2563EB; /* 深蓝色，低饱和度 */
    --yanghua-color: #EA580C; /* 深橙色，低饱和度 */
}

/* 专家会客室标题栏主题色 - 低饱和度 */
.sidebar-header {
    background: var(--theme-main-color) !important;
    color: #fff;
    padding: 20px 0 10px 0;
    text-align: center;
    border-radius: 0 0 12px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}
.sidebar-header h2 {
    font-size: 22px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: #fff;
    letter-spacing: 0.5px;
}
.sidebar-header p {
    font-size: 14px;
    color: rgba(255,255,255,0.8);
    margin: 0;
}

/* 专家列表整体 */
.expert-list {
    padding: 16px 0 0 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* 单个专家卡片 - 低饱和度 */
.expert-card {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 12px 16px;
    position: relative;
    transition: all 0.2s ease;
    cursor: pointer;
    border: 1px solid #f3f4f6;
}
.expert-card:hover {
    box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    border-color: var(--theme-main-color);
    transform: translateY(-1px);
}

/* 头像 */
.expert-avatar {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 12px;
    border: 2px solid #e5e7eb;
    position: relative;
}

/* 在线状态点 - 更柔和 */
.status-dot {
    position: absolute;
    right: 6px;
    bottom: 6px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    background: #10B981; /* 更柔和的绿色 */
    box-shadow: 0 0 4px rgba(16,185,129,0.4);
    z-index: 10;
}

.status-dot.busy {
    background: #F59E0B; /* 更柔和的橙色 */
    box-shadow: 0 0 4px rgba(245,158,11,0.4);
}

/* 状态点脉冲动画 */
@keyframes pulse {
    0% {
        box-shadow: 0 0 4px rgba(0,255,0,0.6), 0 0 8px rgba(0,0,0,0.2);
    }
    50% {
        box-shadow: 0 0 8px rgba(0,255,0,0.8), 0 0 12px rgba(0,0,0,0.3);
    }
    100% {
        box-shadow: 0 0 4px rgba(0,255,0,0.6), 0 0 8px rgba(0,0,0,0.2);
    }
}

.status-dot.busy {
    animation: pulse-busy 2s infinite;
}

@keyframes pulse-busy {
    0% {
        box-shadow: 0 0 4px rgba(255,107,0,0.6), 0 0 8px rgba(0,0,0,0.2);
    }
    50% {
        box-shadow: 0 0 8px rgba(255,107,0,0.8), 0 0 12px rgba(0,0,0,0.3);
    }
    100% {
        box-shadow: 0 0 4px rgba(255,107,0,0.6), 0 0 8px rgba(0,0,0,0.2);
    }
}

/* 专家信息 */
.expert-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}
.expert-name {
    font-size: 16px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 2px;
}
.expert-status {
    font-size: 13px;
    color: #6B7280;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 电话按钮 - 低饱和度 */
.call-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: var(--theme-main-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 8px;
    color: #fff;
    font-size: 18px;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(0,0,0,0.08);
    border: none;
    cursor: pointer;
    position: relative;
}
.call-btn:hover {
    background: var(--theme-dark-color);
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,0,0,0.12);
}
.call-btn:active {
    background: var(--theme-light-color);
    transform: scale(0.98);
}

/* 电话按钮悬浮提示 */
.call-btn[title]::after {
    content: attr(title);
    position: absolute;
    left: 50%;
    top: 110%;
    transform: translateX(-50%);
    background: #222;
    color: #fff;
    font-size: 14px;
    padding: 3px 10px;
    border-radius: 6px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
    z-index: 10;
}
.call-btn:hover[title]::after {
    opacity: 1;
}

/* 专家简介Tooltip */
.expert-card[title] {
    position: relative;
}
.expert-card[title]:hover::after {
    content: attr(title);
    position: absolute;
    left: 50%;
    top: 100%;
    transform: translateX(-50%);
    background: #222;
    color: #fff;
    font-size: 16px;
    padding: 8px 18px;
    border-radius: 8px;
    white-space: pre-line;
    margin-top: 8px;
    z-index: 20;
    min-width: 180px;
    max-width: 320px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.12);
}

/* 搜索框 - 低饱和度 */
.expert-search {
    width: 90%;
    margin: 12px auto 0 auto;
    display: flex;
    align-items: center;
    background: #f9fafb;
    border-radius: 8px;
    padding: 8px 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.04);
    border: 1px solid #e5e7eb;
}
.expert-search input {
    border: none;
    background: transparent;
    outline: none;
    font-size: 14px;
    flex: 1;
    padding: 4px 0;
    color: #374151;
}
.expert-search input::placeholder {
    color: #9CA3AF;
}
.expert-search .search-icon {
    color: #6B7280;
    font-size: 16px;
    margin-right: 8px;
    cursor: pointer;
}
.expert-search .mic-icon {
    color: var(--theme-main-color);
    font-size: 18px;
    margin-left: 8px;
    cursor: pointer;
}

/* 主容器布局调整 */
.main-container {
    display: flex;
    height: 100vh;
    background: #f5f5f5;
}

body {
    background: #F6F7FA !important;
}

.main-container {
    background: #F3F4F6 !important;
    min-height: 100vh;
}

/* 专家边栏 - 减少左边距 */
.expert-sidebar {
    width: 320px;
    background: #fff;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    margin-left: 0; /* 减少左边距 */
    box-shadow: 2px 0 8px rgba(0,0,0,0.1);
}

/* 主题区域 - 适当调整 */
.theme-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #fff;
    margin-left: 0; /* 减少左边距 */
} 