<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能电视鼠标操作界面 - 老年友好版</title>
    <link rel="stylesheet" href="tv-styles.css">
    <link rel="stylesheet" href="theme-styles.css">
    <!-- 引入必要的库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/common.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.min.css" />
</head>

<body>
    <div class="main-container">
        <!-- 左栏 - 专家会客室 -->
        <div class="expert-sidebar">
            <div class="sidebar-header">
                <h2>🏠 专家会客室</h2>
                <p>点击头像与专家对话</p>
            </div>
            <!-- 专家搜索框 -->
            <div class="expert-search">
                <span class="search-icon">🔍</span>
                <input type="text" id="expert-search-input" placeholder="搜索专家姓名/领域..." />
                <span class="mic-icon" title="语音搜索">🎤</span>
            </div>
            <div class="expert-list" id="expert-list">
                <!-- 专家列表将在这里动态生成 -->
            </div>
        </div>

        <!-- 右栏 - 主题交互与聊天区 -->
        <div class="theme-area">
            <!-- 顶部主题标签栏 -->
            <div class="theme-header" id="theme-header">
                <div class="theme-tabs" id="theme-tabs">
                    <div class="theme-tab active" data-theme="yangsheng">养生</div>
                    <div class="theme-tab" data-theme="sannong">三农</div>
                    <div class="theme-tab" data-theme="dangjian">党建</div>
                    <div class="theme-tab" data-theme="jianshen">健身</div>
                    <div class="theme-tab" data-theme="yanghua">养花</div>
                </div>
            </div>

            <!-- 磁贴交互区域 -->
            <div class="content-area" id="tile-content-area">
                <div class="tile-container" id="tile-container">
                    <!-- 磁贴将在这里动态生成 -->
                </div>
            </div>

            <!-- 聊天界面区域 (结构与 index.html 右侧聊天区域一致) -->
            <div class="chat-area" id="chat-area" style="display: none;">
                <div class="chat-header">
                    <span id="back-to-tiles" class="back-to-tiles" onclick="themeManager.goBackToTiles()" style="cursor: pointer;">← 返回磁贴菜单</span>
                    <span id="chat-agent-name"></span>
                </div>
                <div class="messages-container" id="messages-container">
                    <!-- 消息将在这里显示 -->
                </div>
                <div class="input-area">
                    <div class="input-container">
                        <div class="user-avatar-container">
                            <img src="touxiang/3.png" class="user-avatar" alt="用户头像" />
                        </div>
                        <textarea id="user-input" class="user-input" placeholder="请输入您的问题..."
                            onkeydown="handleKeyDown(event)"></textarea>
                        <button id="send-btn" class="send-btn" onclick="themeManager.sendMessageFromInput()">
                            发送
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入应用文件 -->
    <script src="tv-data.js"></script>
    <script src="api.js"></script>
    <script src="theme-manager.js"></script>
    <script src="tv-app.js"></script>

    <script>
        // Coze API 配置
        const COZE_CONFIG = {
            token: 'sat_sWfwyuKNho8ZHtkm2nmiXsXlFntAptsaa9XC6HpofAA3XLpTRoS5gGmkCJ2LpwaP',
        };

        // 专家配置
        const EXPERTS = [
            {
                id: 'expert1',
                name: '养生专家',
                avatar: 'touxiang/11.png',
                bot_id: '7530915278943830051',
                specialty: '中医养生、食疗保健'
            },
            {
                id: 'expert2', 
                name: '农业专家',
                avatar: 'touxiang/12.png',
                bot_id: '7530940616785051683',
                specialty: '种植技术、养殖管理'
            },
            {
                id: 'expert3',
                name: '党建专家', 
                avatar: 'touxiang/13.png',
                bot_id: '7528256246898622518',
                specialty: '党史学习、理论宣讲'
            },
            {
                id: 'expert4',
                name: '健身专家',
                avatar: 'touxiang/16.png', 
                bot_id: '7524620947187056679',
                specialty: '运动指导、康复训练'
            },
            {
                id: 'expert5',
                name: '园艺专家',
                avatar: 'touxiang/19.png',
                bot_id: '7523880023146168366',
                specialty: '花卉种植、园艺技巧'
            }
        ];

        // 主题与专家的映射关系
        const THEME_EXPERT_MAP = {
            'yangsheng': 'expert1', // 养生 -> 养生专家
            'sannong': 'expert2',   // 三农 -> 农业专家
            'dangjian': 'expert3',  // 党建 -> 党建专家
            'jianshen': 'expert4',  // 健身 -> 健身专家
            'yanghua': 'expert5'    // 养花 -> 园艺专家
        };
    </script>
</body>
</html>