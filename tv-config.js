// Coze API 配置
const COZE_CONFIG = {
    token: 'sat_sWfwyuKNho8ZHtkm2nmiXsXlFntAptsaa9XC6HpofAA3XLpTRoS5gGmkCJ2LpwaP',
    api_base: 'https://api.coze.cn/open_api/v2',
    ws_base: 'wss://ws.coze.cn/v1/audio'
};

// 专家配置 - 更新为新的bot_id
const EXPERTS = [
    {
        id: 'expert1',
        name: '养生专家',
        avatar: 'touxiang/11.png',
        bot_id: '7530915278943830051',
        specialty: '中医养生、食疗保健'
    },
    {
        id: 'expert2', 
        name: '农业专家',
        avatar: 'touxiang/12.png',
        bot_id: '7530940616785051683',
        specialty: '种植技术、养殖管理'
    },
    {
        id: 'expert3',
        name: '党建专家', 
        avatar: 'touxiang/13.png',
        bot_id: '7528256246898622518',
        specialty: '党史学习、理论宣讲'
    },
    {
        id: 'expert4',
        name: '健身专家',
        avatar: 'touxiang/16.png', 
        bot_id: '7524620947187056679',
        specialty: '运动指导、康复训练'
    },
    {
        id: 'expert5',
        name: '园艺专家',
        avatar: 'touxiang/19.png',
        bot_id: '7523880023146168366',
        specialty: '花卉种植、园艺技巧'
    }
];

// 主题与专家的映射关系
const THEME_EXPERT_MAP = {
    'yangsheng': 'expert1', // 养生 -> 养生专家
    'sannong': 'expert2',   // 三农 -> 农业专家
    'dangjian': 'expert3',  // 党建 -> 党建专家
    'jianshen': 'expert4',  // 健身 -> 健身专家
    'yanghua': 'expert5'    // 养花 -> 园艺专家
};