<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .config-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .agent-item {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
        }

        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 3px;
        }

        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 3px;
        }
    </style>
</head>

<body>
    <h1>配置系统测试</h1>

    <div id="status"></div>

    <div class="config-section">
        <h2>分类配置</h2>
        <div id="categories"></div>
    </div>

    <div class="config-section">
        <h2>智能体配置</h2>
        <div id="agents"></div>
    </div>

    <script>
        async function testConfig() {
            const statusDiv = document.getElementById('status');
            const categoriesDiv = document.getElementById('categories');
            const agentsDiv = document.getElementById('agents');

            try {
                // 尝试从 config.json 读取
                let response = await fetch('config.json');
                let configData;

                if (response.ok) {
                    configData = await response.json();
                    statusDiv.innerHTML = '<div class="success">✅ 成功从 config.json 读取配置</div>';
                } else {
                    // 尝试从 agents-config.json 读取
                    response = await fetch('agents-config.json');
                    if (response.ok) {
                        configData = await response.json();
                        statusDiv.innerHTML = '<div class="success">✅ 成功从 agents-config.json 读取配置（备用）</div>';
                    } else {
                        throw new Error('无法读取任何配置文件');
                    }
                }

                // 显示分类
                if (configData.categories && configData.categories.length > 0) {
                    categoriesDiv.innerHTML = configData.categories.map(cat =>
                        `<div class="agent-item"><strong>${cat.name}</strong> (ID: ${cat.id})</div>`
                    ).join('');
                } else {
                    categoriesDiv.innerHTML = '<div class="error">❌ 没有找到分类配置</div>';
                }

                // 显示智能体
                if (configData.agents && configData.agents.length > 0) {
                    agentsDiv.innerHTML = configData.agents.map(agent => {
                        const category = configData.categories?.find(c => c.id === agent.category);
                        return `<div class="agent-item">
                            <strong>${agent.name}</strong><br>
                            描述: ${agent.description}<br>
                            分类: ${category ? category.name : '未知'}<br>
                            Bot ID: ${agent.bot_id}<br>
                            头像: ${agent.avatar}
                        </div>`;
                    }).join('');
                } else {
                    agentsDiv.innerHTML = '<div class="error">❌ 没有找到智能体配置</div>';
                }

            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ 配置读取失败: ${error.message}</div>`;
                console.error('配置读取失败:', error);
            }
        }

        // 页面加载时测试配置
        window.onload = testConfig;
    </script>
</body>

</html>