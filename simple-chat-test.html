<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单聊天测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .chat-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: 600px;
            display: flex;
            flex-direction: column;
        }
        
        .chat-header {
            padding: 20px;
            background: #7C3AED;
            color: white;
            border-radius: 10px 10px 0 0;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
        }
        
        .messages-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            border-bottom: 1px solid #eee;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            flex-shrink: 0;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: #7C3AED;
            color: white;
        }
        
        .message.assistant .message-content {
            background: #f0f0f0;
            color: #333;
        }
        
        .input-area {
            padding: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .input-box {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
        }
        
        .input-box:focus {
            border-color: #7C3AED;
        }
        
        .send-btn {
            padding: 12px 24px;
            background: #7C3AED;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        
        .send-btn:hover {
            background: #6D28D9;
        }
        
        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .loading {
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            养生专家 - 简单聊天测试
        </div>
        
        <div class="messages-area" id="messages-area">
            <!-- 消息将在这里显示 -->
        </div>
        
        <div class="input-area">
            <input type="text" class="input-box" id="user-input" placeholder="请输入您的问题..." />
            <button class="send-btn" id="send-btn" onclick="sendMessage()">发送</button>
        </div>
    </div>

    <script>
        // Coze API 配置
        const COZE_CONFIG = {
            token: 'sat_sWfwyuKNho8ZHtkm2nmiXsXlFntAptsaa9XC6HpofAA3XLpTRoS5gGmkCJ2LpwaP',
        };
        
        // 养生专家配置
        const EXPERT_CONFIG = {
            bot_id: '7530915278943830051',
            name: '养生专家',
            avatar: 'touxiang/11.png'
        };
        
        let currentConversationId = null;
        
        // 添加消息到界面
        function addMessage(role, content) {
            const messagesArea = document.getElementById('messages-area');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const avatar = role === 'user' ? 'touxiang/3.png' : EXPERT_CONFIG.avatar;
            
            messageDiv.innerHTML = `
                <img src="${avatar}" alt="${role}" class="message-avatar" />
                <div class="message-content">${content}</div>
            `;
            
            messagesArea.appendChild(messageDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
            
            return messageDiv;
        }
        
        // 更新消息内容（用于流式输出）
        function updateMessage(messageDiv, content) {
            const contentDiv = messageDiv.querySelector('.message-content');
            contentDiv.textContent = content;
        }
        
        // 发送消息
        async function sendMessage() {
            const userInput = document.getElementById('user-input');
            const sendBtn = document.getElementById('send-btn');
            const prompt = userInput.value.trim();
            
            if (!prompt) return;
            
            // 禁用输入和按钮
            userInput.disabled = true;
            sendBtn.disabled = true;
            
            // 添加用户消息
            addMessage('user', prompt);
            
            // 清空输入框
            userInput.value = '';
            
            // 添加AI消息占位符
            const aiMessageDiv = addMessage('assistant', '正在思考...');
            
            try {
                // 调用流式API
                await callStreamAPI(prompt, aiMessageDiv);
            } catch (error) {
                console.error('发送消息失败:', error);
                updateMessage(aiMessageDiv, '抱歉，发生了错误，请重试。');
            } finally {
                // 重新启用输入和按钮
                userInput.disabled = false;
                sendBtn.disabled = false;
                userInput.focus();
            }
        }
        
        // 调用流式API
        async function callStreamAPI(prompt, messageDiv) {
            const url = 'https://api.coze.cn/v3/chat';
            const headers = {
                'Authorization': `Bearer ${COZE_CONFIG.token}`,
                'Content-Type': 'application/json'
            };

            const body = {
                bot_id: EXPERT_CONFIG.bot_id,
                user_id: 'user_' + Date.now(),
                stream: true,
                auto_save_history: false,  // 修复：stream为true时，auto_save_history必须为false
                additional_messages: [{
                    role: 'user',
                    content: prompt,
                    content_type: 'text'
                }]
            };

            if (currentConversationId) {
                body.conversation_id = currentConversationId;
            }

            console.log('发送请求:', body);

            const response = await fetch(url, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(body)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('API请求失败:', response.status, errorText);
                throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
            }

            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let fullResponse = '';
            let currentEvent = '';

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('event: ')) {
                        // 解析事件类型
                        currentEvent = line.slice(7).trim();
                        console.log('事件类型:', currentEvent);
                    } else if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') continue;

                        try {
                            const parsed = JSON.parse(data);
                            console.log('收到数据:', parsed);

                            // 修复：根据官方文档，流式输出在conversation.message.delta事件中的content字段
                            if (currentEvent === 'conversation.message.delta') {
                                const content = parsed.content;
                                if (content) {
                                    fullResponse += content;
                                    updateMessage(messageDiv, fullResponse);
                                }
                            } else if (currentEvent === 'conversation.chat.completed') {
                                currentConversationId = parsed.conversation_id;
                                console.log('对话完成，会话ID:', currentConversationId);
                            }
                        } catch (e) {
                            console.log('解析数据失败:', e, data);
                        }
                    }
                }
            }

            if (!fullResponse) {
                updateMessage(messageDiv, '抱歉，没有收到回复，请重试。');
            }
        }
        
        // 回车发送
        document.getElementById('user-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // 页面加载完成后聚焦输入框
        window.addEventListener('load', function() {
            document.getElementById('user-input').focus();
        });
    </script>
</body>
</html>
