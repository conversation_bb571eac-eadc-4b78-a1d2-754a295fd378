<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天系统 - 选择页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Helvetica Neue", "PingFang SC", sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
        }

        .header {
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 32px;
            color: #2d3748;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 16px;
            color: #718096;
            line-height: 1.6;
        }

        .options {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 40px;
        }

        .option-card {
            background: #f7fafc;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 30px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .option-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.5s;
        }

        .option-card:hover::before {
            left: 100%;
        }

        .option-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }

        .option-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }

        .option-title {
            font-size: 20px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 10px;
        }

        .option-description {
            font-size: 14px;
            color: #718096;
            line-height: 1.5;
        }

        .footer {
            border-top: 1px solid #e2e8f0;
            padding-top: 30px;
            color: #a0aec0;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .container {
                padding: 40px 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .option-card {
                padding: 20px;
            }
            
            .option-icon {
                font-size: 36px;
            }
            
            .option-title {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI聊天系统</h1>
            <p>选择您需要的聊天界面类型，体验不同的交互方式</p>
        </div>

        <div class="options">
            <a href="index.html" class="option-card">
                <span class="option-icon">💬</span>
                <div class="option-title">纯自定义聊天页面</div>
                <div class="option-description">
                    完全自定义的聊天界面，支持多智能体切换，Markdown渲染，适合需要高度定制化的场景
                </div>
            </a>

            <a href="final-custom-layout-demo.html" class="option-card">
                <span class="option-icon">🎛️</span>
                <div class="option-title">自定义边栏+SDK聊天页面</div>
                <div class="option-description">
                    左侧自定义智能体选择边栏，右侧集成官方SDK聊天窗口，兼具定制性和官方功能完整性
                </div>
            </a>

            <a href="tv-elderly-interface.html" class="option-card">
                <span class="option-icon">📺</span>
                <div class="option-title">智能电视鼠标操作界面</div>
                <div class="option-description">
                    专为老年人设计的大字体、大按钮界面，支持磁贴点击式交互和专家语音通话功能
                </div>
            </a>
        </div>

        <div class="footer">
            <p>© 2024 AI聊天系统 - 基于Coze平台构建</p>
        </div>
    </div>
</body>
</html>