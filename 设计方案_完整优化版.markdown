# 老年友好型智能问答平台前端改版设计方案（完整优化版）

**设计目标**：打造一个操作简便、信息清晰、交互友好的智能问答平台，专为老年用户优化，提供直观的交互体验、清晰的语音反馈和便捷的专家咨询功能。

---

## 一、页面整体结构（分左右两栏）

| 区域 | 内容说明 |
|------|----------|
| 左栏 | **专家会客室**：显示专家列表，支持语音通话、搜索和筛选功能，方便用户快速联系专家。 |
| 右栏 | **主题交互与问答区**：支持主题切换、磁贴式话题引导、自动Prompt生成、语音播报及自由提问功能。 |

---

## 二、左栏：专家会客室（增强版）

**🔷 模块功能说明**：
- **标题**： “专家会客室” - 使用大字体（20px），高对比度，醒目易读。
- **专家头像列表**：
  - 每个条目包含：专家头像（清晰、亲和）、专家名称（18px，粗体）、绿色在线状态点（直径8px）、状态文字（“在线”/“忙碌”，14px）。
  - **电话图标按钮**：右侧，点击发起语音通话。
  - **新增**：专家简介悬浮提示，显示擅长领域、服务时间等。
  - **新增**：搜索/筛选功能，顶部搜索框支持按姓名或领域筛选。
  - **新增**：语音搜索支持，用户可通过语音输入查找专家。
- **新增**：离线提示：当网络断开时，显示“当前无网络，请稍后重试”提示，并提供最近联系的专家缓存列表。

**🧱 元素结构（细化）**：

| 元素 | 功能 | 样式/交互细节 |
|------|------|---------------|
| 专家头像 | 显示专家名称、头像、在线状态 | 头像：圆形，60x60px，清晰。<br>名称：18px，粗体。<br>状态点：绿色（在线）/黄色（忙碌），右下角对齐。<br>悬浮提示：Tooltip显示简介，文字16px。 |
| 电话按钮 | 发起语音通话，悬浮提示“点击通话” | 图标：电话图标（Font Awesome phone），40x40px。<br>样式：背景协调，点击反馈（背景变深、放大）。<br>悬浮提示：16px文字。 |
| 在线状态指示 | 显示“在线”/“忙碌” | 圆点：8px，绿色/黄色。<br>文字：14px，高对比度。 |
| 搜索框 | 快速查找专家 | 输入框：带搜索图标，16px文字。<br>交互：实时过滤，支持语音输入（麦克风图标）。 |

**🎯 行为逻辑（优化）**：
- **点击专家头像**：
  - 默认：选中专家，右侧显示专家简介或历史对话摘要。
  - 可选：切换至文字聊天模式（若支持）。
- **点击电话按钮**：
  - 右栏切换为通话UI：
    - 全屏放大专家头像（80x80px），下方显示名称（18px）。
    - 通话状态动画（声波/波纹），显示“通话中”。
    - **结束通话按钮**：红色，60x40px，文字“结束通话”（18px）。
    - **新增**：通话时长计时器（14px，实时更新）。
    - **新增**：音量调节/静音按钮，图标40x40px，易点击。
  - **新增**：通话失败提示：若连接失败，显示“连接失败，请重试”并提供“重拨”按钮。
  - 通话优先，暂停其他交互，结束后返回原页面状态（保留磁贴层级）。
- **新增**：离线模式：缓存最近5位专家信息，允许查看简介但禁用通话按钮，提示“请连接网络以通话”。

---

## 三、右栏：点击式交互 + 模型问答区（深度优化）

**🔷 页面结构模块**：
- **顶部标签栏（主题切换）**：
  - 横向按钮，尺寸60x40px，文字18px，高对比度。
  - 默认高亮“三农”，点击平滑切换（300ms过渡动画）。
  - 标签：三农 / 养生 / 党建 / 健身 / 养花。
  - **新增**：悬浮提示显示主题说明（16px，简短）。
  - **新增**：语音切换支持，用户可语音说出主题名称切换。
- **磁贴点击交互区域（一级话题）**：
  - 4×4网格，磁贴120x120px，圆角8px。
  - 内容：主题下一级话题（如三农 → 种植技术、养殖技术）。
  - 样式：大图标（64x64px）+标题（16px，粗体），点击动画（缩放1.1倍）。
  - **新增**：磁贴描述（14px，灰色，单行），置于磁贴下方。
- **二级话题磁贴区域**：
  - 4×4网格，磁贴100x100px，颜色/边框区分层级。
  - **新增**：顶部“返回上一级”按钮（50x40px，文字16px，蓝色背景）。
  - **新增**：面包屑导航（三农 > 种植技术，14px，点击可跳转）。
- **Prompt拼接与大模型响应区**：
  - **自动生成Prompt**：二级磁贴点击后，系统拼接Prompt并提交。
  - **Loading状态**：显示“正在生成回答...”（16px，带旋转动画）。
  - **回答展示区**：
    - 聊天框气泡，自下而上，最新回答在底部。
    - 气泡样式：内边距16px，行距1.5，背景浅灰（#F5F5F5），文字16-18px。
    - 用户Prompt（自动生成）与模型回答气泡颜色区分（用户：蓝色，模型：白色）。
  - **语音播报（TTS）**：
    - 自动播报，语速默认1.0x（可调0.8x-1.2x）。
    - **新增**：播报控制面板（底部，包含播放/暂停/重播/语速/音量，图标40x40px）。
    - **新增**：文本高亮，播报时逐句高亮（黄色背景）。
  - **新增**：操作按钮（气泡下方）：
    - “重新提问”（蓝色，50x40px）。
    - “分享”（灰色，分享至微信/短信）。
    - “收藏”（黄色，保存至个人收藏夹）。
  - **新增**：自由提问输入框（底部，16px，支持语音输入，麦克风图标40x40px）。
  - **新增**：错误处理：若模型无响应，显示“回答生成失败，请重试”并提供“重试”按钮。

**🎯 点击流程完整示意**：
1. **打开页面**：默认“三农”一级磁贴，左侧“专家会客室”可见。
2. **点击一级磁贴“种植技术”**：右侧平滑切换至二级磁贴（水稻、玉米等），显示“返回”按钮和面包屑导航。
3. **点击“水稻”**：生成Prompt（如“老年人种植水稻的病虫害防治要点有哪些？”），显示加载动画，回答后展示气泡+语音播报。
4. **用户交互**：可点击“重新提问”、自由输入问题或切换主题。
5. **新增**：若网络断开，显示“离线模式，仅支持浏览历史回答”，缓存最近10条回答。

---

## 四、界面状态切换逻辑总结

| 用户行为 | 页面反应 | 交互细节与注意事项 |
|----------|----------|--------------------|
| 点击专家电话按钮 | 切换为通话UI | 平滑切换（300ms），显示通话状态、时长、结束按钮。失败时提示“重拨”。 |
| 点击主题标签 | 展示一级磁贴 | 过渡动画，高亮新标签，支持语音切换，保存问答状态。 |
| 点击一级磁贴 | 进入二级磁贴 | 显示“返回”按钮+面包屑导航，动画过渡。 |
| 点击二级磁贴 | 生成Prompt，展示回答+播报 | 加载动画，气泡清晰，语音可控，错误提示+重试。 |
| 点击返回按钮 | 返回一级磁贴 | 即时响应，淡入动画（200ms）。 |
| 模型回答完成 | 显示回答，允许操作 | 提供“重新提问/分享/收藏”，自由输入框始终可见。 |
| 自由提问 | 提交问题，显示新气泡 | 支持语音/键盘输入，发送按钮醒目（50x40px）。 |

---

## 五、其他老年友好型设计考量（优化版）

- **字体与对比度**：
  - 最小16px，核心内容18-20px。
  - 对比度符合WCAG 2.1 AA标准（4.5:1）。
  - 字体：思源黑体/微软雅黑，笔画清晰。
- **操作区域**：
  - 按钮最小44x44px，间距至少12px。
  - 触摸区域优化，防止误触。
- **视觉与听觉反馈**：
  - 焦点状态：2px蓝色边框+背景高亮。
  - 点击反馈：缩放动画（1.1倍，200ms）+“操作成功”语音提示。
  - 进度指示：加载动画（旋转圆圈）+文字提示（16px）。
- **简洁布局**：
  - 信息分层：每屏不超过8个交互元素。
  - 移除装饰性动画，保留功能性动画。


---

## 六、各主题磁贴内容与提示词列表（优化版）

### Prompt Design Principles
- **Simplicity**: Clear, jargon-free language.
- **Action-Oriented**: Focus on practical advice.
- **Contextual Relevance**: Tailored to elderly users’ daily needs.
- **Voice-Friendly**: Short sentences, TTS-compatible.
- **Consistency**: Format: "请[动词][具体内容][适用于老年人的方式/注意事项]".






