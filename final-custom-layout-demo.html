<!doctype html>
<html lang="zh-CN">

<head>
    <meta charset="utf-8" />
    <title>最终嵌入式演示</title>
    <link rel="stylesheet" href="style.css" />
    <style>
        html,
        body {
            height: 100%;
            margin: 0;
            font-family: "Helvetica", "PingFang SC", sans-serif;
        }

        .chat-container {
            display: flex;
            height: 100vh;
            background: #f5f8ff;
        }

        .agent-sidebar {
            width: 260px;
            background: white;
            border-right: 1px solid #e0e0e0;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .agent-list {
            flex: 1;
            padding: 15px;
        }

        .agent-category-title {
            font-size: 14px;
            font-weight: 600;
            color: #64748B;
            margin: 10px 0;
            padding: 5px 10px;
            background: #f1f5f9;
            border-radius: 4px;
        }

        .agent-item {
            display: flex;
            align-items: center;
            padding: 12px;
            cursor: pointer;
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .agent-item:hover {
            background-color: #f0f8ff;
        }

        .agent-item.selected {
            background: linear-gradient(135deg, #e6f3ff 0%, #f0f8ff 100%);
            border-color: #3b82f6;
            font-weight: bold;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
        }

        .name {
            font-weight: 600;
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100vh;
            background: white;
            min-width: 0;
            position: relative;
            overflow: hidden;
        }

        .chat-header {
            padding: 15px 20px;
            background: #f0f8ff;
            border-bottom: 1px solid #e0e7ff;
            font-size: 14px;
            color: #3b82f6;
            height: 56px;
            box-sizing: border-box;
        }

        /* 确保SDK聊天窗口完全填充 */
        #coze-chat-container * {
            box-sizing: border-box;
        }

        /* 强制SDK内容填充整个容器 */
        #coze-chat-container>div,
        #coze-chat-container iframe {
            width: 100% !important;
            height: 100% !important;
            max-width: none !important;
            max-height: none !important;
        }
    </style>
    <!-- Using the SDK version from your working example -->
    <script src="https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.15/libs/cn/index.js"></script>
</head>

<body>
    <div class="chat-container">
        <div class="agent-sidebar">
            <div class="sidebar-header">
                <h3>🤖 AI 智能助手</h3>
            </div>
            <div class="agent-list" id="agent-list"></div>
        </div>
        <div class="chat-area">
            <div class="chat-header"><span>与 <span id="chat-agent-name">AI助手</span> 对话</span></div>
            <div id="coze-chat-container"
                style="position: absolute; top: 56px; left: 0; right: 0; bottom: 0; width: 100%;"></div>
        </div>
    </div>

    <script>
        window.onload = async function () {
            // --- Configuration ---
            const SAT_TOKEN = 'sat_sWfwyuKNho8ZHtkm2nmiXsXlFntAptsaa9XC6HpofAA3XLpTRoS5gGmkCJ2LpwaP';

            // --- DOM Elements ---
            const agentListEl = document.getElementById('agent-list');
            const chatAgentNameEl = document.getElementById('chat-agent-name');
            const chatContainerEl = document.getElementById('coze-chat-container');

            // --- Global State ---
            let client = null;
            let agentConfigs = {};

            // --- Functions ---

            // Load agent configurations from local JSON
            async function loadAgentConfigs() {
                // Hardcode the configuration to eliminate fetch/file-loading issues.
                const hardcodedData = {
                    "agents": [
                        { "id": "mdgy8weu9oeb0", "name": "抖音账号分析专家", "description": "抖音账号分析", "category": "mdgzqt479yixm", "bot_id": "7523880023146168366", "avatar": "touxiang/abgag.png" },
                        { "id": "mdgy8weufsow9", "name": "公文写作专家", "description": "专业写作公文", "category": "mdgzqt479vyew", "bot_id": "7528255758204076072", "avatar": "touxiang/8.png" },
                        { "id": "mdgy8weu3xbkt", "name": "日报专家", "description": "帮助写一份日报", "category": "mdgzqt479vyew", "bot_id": "7528256246898622518", "avatar": "touxiang/12.png" },
                        { "id": "mdgy8weubknlv", "name": "小红书图文总结专家", "description": "总结图文笔记内容", "category": "mdgzqt479yixm", "bot_id": "7524620947187056679", "avatar": "touxiang/aga.png" }
                    ],
                    "categories": [
                        { "id": "mdgzqt479yixm", "name": "媒体部" },
                        { "id": "mdgzqt47ynyhu", "name": "法律部" },
                        { "id": "mdgzqt479vyew", "name": "行政部" }
                    ]
                };

                const configs = {};
                const categoryMap = {};

                if (hardcodedData.categories) {
                    hardcodedData.categories.forEach(cat => categoryMap[cat.id] = cat.name);
                }

                if (hardcodedData.agents) {
                    hardcodedData.agents.forEach(agent => {
                        configs[agent.name] = {
                            ...agent,
                            categoryName: categoryMap[agent.category] || '未分类'
                        };
                    });
                }
                return configs;
            }

            // Render the list of agents on the left sidebar
            function renderAgentList(configs) {
                const agentsByCategory = {};
                Object.values(configs).forEach(agent => {
                    if (!agentsByCategory[agent.categoryName]) {
                        agentsByCategory[agent.categoryName] = [];
                    }
                    agentsByCategory[agent.categoryName].push(agent);
                });

                agentListEl.innerHTML = ''; // Clear list

                for (const categoryName in agentsByCategory) {
                    const categoryTitle = document.createElement('div');
                    categoryTitle.className = 'agent-category-title';
                    categoryTitle.textContent = categoryName;
                    agentListEl.appendChild(categoryTitle);

                    agentsByCategory[categoryName].forEach(agent => {
                        const agentItem = document.createElement('div');
                        agentItem.className = 'agent-item';
                        agentItem.innerHTML = `<img src="${agent.avatar}" class="avatar" /><div class="name">${agent.name}</div>`;
                        agentItem.onclick = () => selectAgent(agent, agentItem);
                        agentListEl.appendChild(agentItem);
                    });
                }
            }

            // Handle agent selection
            function selectAgent(agent, element) {
                document.querySelectorAll('.agent-item').forEach(el => el.classList.remove('selected'));
                element.classList.add('selected');
                chatAgentNameEl.textContent = agent.name;

                if (client) {
                    client.destroy();
                }
                chatContainerEl.innerHTML = '';

                client = new CozeWebSDK.WebChatClient({
                    config: {
                        type: 'bot',
                        bot_id: agent.bot_id,
                    },
                    auth: {
                        type: 'token',
                        token: SAT_TOKEN,
                        onRefreshToken: () => SAT_TOKEN
                    },
                    userInfo: {
                        id: 'user_final_demo',
                        nickname: '本地用户',
                    },
                    ui: {
                        chatBot: { el: chatContainerEl, title: agent.name },
                        header: { isShow: false },
                        asstBtn: { isNeed: false },
                        footer: { isShow: false },
                        size: { width: '100%', height: '100%' },
                    },
                });

                setTimeout(() => {
                    if (client) {
                        client.showChatBot();
                        console.log(`Client for ${agent.name} initialized and shown.`);
                    }
                }, 100);
            }

            // --- Initialization ---
            agentConfigs = await loadAgentConfigs();
            renderAgentList(agentConfigs);

            // Auto-select the first agent
            if (agentListEl.firstChild) {
                // Find the first actual agent item, skipping category titles
                const firstAgent = agentListEl.querySelector('.agent-item');
                if (firstAgent) {
                    firstAgent.click();
                }
            }
        };
    </script>
</body>

</html>