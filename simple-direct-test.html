<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .chat-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            height: 600px;
            display: flex;
            flex-direction: column;
        }
        
        .chat-header {
            padding: 20px;
            background: #7C3AED;
            color: white;
            border-radius: 10px 10px 0 0;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
        }
        
        .messages-area {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            border-bottom: 1px solid #eee;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            flex-shrink: 0;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
            white-space: pre-wrap;
        }
        
        .message.user .message-content {
            background: #7C3AED;
            color: white;
        }
        
        .message.assistant .message-content {
            background: #f0f0f0;
            color: #333;
        }
        
        .input-area {
            padding: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .input-box {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
        }
        
        .input-box:focus {
            border-color: #7C3AED;
        }
        
        .send-btn {
            padding: 12px 24px;
            background: #7C3AED;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
        
        .send-btn:hover {
            background: #6D28D9;
        }
        
        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .loading {
            color: #666;
            font-style: italic;
        }
        
        .error {
            color: #dc2626;
            background: #fef2f2;
            border: 1px solid #fecaca;
            padding: 10px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            养生专家 - 直接API测试
        </div>
        
        <div class="messages-area" id="messages-area">
            <div class="message assistant">
                <img src="touxiang/11.png" alt="assistant" class="message-avatar" />
                <div class="message-content">您好！我是养生专家，请问有什么养生问题需要咨询吗？</div>
            </div>
        </div>
        
        <div class="input-area">
            <input type="text" class="input-box" id="user-input" placeholder="请输入您的问题..." />
            <button class="send-btn" id="send-btn" onclick="sendMessage()">发送</button>
        </div>
    </div>

    <script>
        // Coze API 配置
        const COZE_CONFIG = {
            token: 'sat_sWfwyuKNho8ZHtkm2nmiXsXlFntAptsaa9XC6HpofAA3XLpTRoS5gGmkCJ2LpwaP',
        };
        
        // 养生专家配置
        const EXPERT_CONFIG = {
            bot_id: '7530915278943830051',
            name: '养生专家',
            avatar: 'touxiang/11.png'
        };
        
        // 添加消息到界面
        function addMessage(role, content) {
            const messagesArea = document.getElementById('messages-area');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            const avatar = role === 'user' ? 'touxiang/3.png' : EXPERT_CONFIG.avatar;
            
            messageDiv.innerHTML = `
                <img src="${avatar}" alt="${role}" class="message-avatar" />
                <div class="message-content">${content}</div>
            `;
            
            messagesArea.appendChild(messageDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
            
            return messageDiv;
        }
        
        // 显示调试信息
        function showDebugInfo(info) {
            const messagesArea = document.getElementById('messages-area');
            const debugDiv = document.createElement('div');
            debugDiv.className = 'debug-info';
            debugDiv.textContent = JSON.stringify(info, null, 2);
            messagesArea.appendChild(debugDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }
        
        // 显示错误信息
        function showError(message) {
            const messagesArea = document.getElementById('messages-area');
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = '错误: ' + message;
            messagesArea.appendChild(errorDiv);
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }
        
        // 发送消息
        async function sendMessage() {
            const userInput = document.getElementById('user-input');
            const sendBtn = document.getElementById('send-btn');
            const prompt = userInput.value.trim();
            
            if (!prompt) return;
            
            // 禁用输入和按钮
            userInput.disabled = true;
            sendBtn.disabled = true;
            sendBtn.textContent = '发送中...';
            
            // 添加用户消息
            addMessage('user', prompt);
            
            // 清空输入框
            userInput.value = '';
            
            try {
                // 调用直接API
                const response = await callDirectAPI(prompt);
                
                if (response.success) {
                    addMessage('assistant', response.response);
                } else {
                    showError(response.error || '未知错误');
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                showError('网络错误: ' + error.message);
            } finally {
                // 重新启用输入和按钮
                userInput.disabled = false;
                sendBtn.disabled = false;
                sendBtn.textContent = '发送';
                userInput.focus();
            }
        }
        
        // 调用直接API（使用最简单的格式）
        async function callDirectAPI(prompt) {
            const url = 'https://api.coze.cn/v3/chat';
            const headers = {
                'Authorization': `Bearer ${COZE_CONFIG.token}`,
                'Content-Type': 'application/json'
            };
            
            // 使用最简单的消息格式
            const body = {
                bot_id: EXPERT_CONFIG.bot_id,
                user_id: 'user_' + Date.now(),
                query: prompt,
                stream: false
            };
            
            console.log('发送请求:', body);
            showDebugInfo({ type: 'request', data: body });
            
            const response = await fetch(url, {
                method: 'POST',
                headers: headers,
                body: JSON.stringify(body)
            });
            
            console.log('响应状态:', response.status);
            
            if (!response.ok) {
                const errorText = await response.text();
                console.error('API请求失败:', response.status, errorText);
                showDebugInfo({ type: 'error', status: response.status, error: errorText });
                return {
                    success: false,
                    error: `HTTP ${response.status}: ${errorText}`
                };
            }
            
            const data = await response.json();
            console.log('收到响应:', data);
            showDebugInfo({ type: 'response', data: data });
            
            // 尝试多种可能的响应格式
            if (data.code === 0) {
                // 格式1: data.messages
                if (data.data && data.data.messages && data.data.messages.length > 0) {
                    const assistantMsg = data.data.messages.find(msg => 
                        msg.role === 'assistant' || msg.type === 'answer'
                    );
                    if (assistantMsg && assistantMsg.content) {
                        return { success: true, response: assistantMsg.content };
                    }
                }
                
                // 格式2: data.content
                if (data.data && data.data.content) {
                    return { success: true, response: data.data.content };
                }
                
                // 格式3: 直接在data中
                if (data.data && typeof data.data === 'string') {
                    return { success: true, response: data.data };
                }
            }
            
            return {
                success: false,
                error: `无法解析响应: code=${data.code}, 数据结构未知`
            };
        }
        
        // 回车发送
        document.getElementById('user-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // 页面加载完成后聚焦输入框
        window.addEventListener('load', function() {
            document.getElementById('user-input').focus();
        });
    </script>
</body>
</html>
