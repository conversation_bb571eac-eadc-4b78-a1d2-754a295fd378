# Technology Stack

## Frontend Technologies

- **HTML5/CSS3/JavaScript**: Pure vanilla JavaScript implementation
- **Coze Chat SDK**: Version 1.2.0-beta.10 for AI chat integration
- **Markdown Rendering**: 
  - marked.js for markdown parsing
  - highlight.js for syntax highlighting
- **UI Framework**: Custom CSS with WeChat-style design patterns

## Backend/API Integration

- **Coze API**: Direct integration with `https://api.coze.cn/open_api/v2`
- **Authentication**: SAT Token-based authentication
- **Configuration**: JSON-based agent configuration system

## Deployment & Infrastructure

- **Web Server**: Nginx with custom routing configuration
- **Containerization**: Docker with docker-compose
- **Static Hosting**: Nginx serving static files with CORS support

## Build & Development Commands

### Local Development
```bash
# Python HTTP server (recommended)
python -m http.server 8080

# Node.js alternative
npm install -g http-server
http-server -p 8080

# PHP alternative
php -S localhost:8080
```

### Docker Deployment
```bash
# Build and start containers
docker-compose up -d --build

# View logs
docker-compose logs -f chat-app

# Stop services
docker-compose down
```

### Configuration Management
```bash
# Test configuration
open test-config.html

# Backup configuration
cp config.json config.json.backup.$(date +%Y%m%d)
```

## Key Dependencies

- Coze Chat SDK (CDN)
- marked.js (CDN)
- highlight.js (CDN)
- Custom CSS framework
- Nginx for production serving