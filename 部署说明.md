# 智能体聊天系统部署说明

## 系统架构

```
项目根目录/
├── navigation.html                    # 导航页面（首页）
├── index.html                        # 纯自定义聊天界面
├── final-custom-layout-demo.html     # 自定义边栏+SDK聊天界面
├── agent-config.html                 # 配置管理界面
├── test-config.html                  # 配置测试页面
├── main.js                          # 主要JavaScript逻辑
├── style.css                        # 样式文件
├── config.json                      # 新配置文件（推荐）
├── agents-config.json               # 旧配置文件（兼容）
├── nginx.conf                       # Nginx配置文件
├── Dockerfile                       # Docker构建文件
├── docker-compose.yml               # Docker Compose配置
├── touxiang/                        # 头像图片目录
└── 配置使用说明.md                   # 使用说明
```

## 页面访问地址

### 生产环境访问 (***************:1234)

#### 主要页面
- **导航页面**: http://***************:1234/ (默认首页)
- **纯自定义聊天页面**: http://***************:1234/index.html
- **自定义边栏+SDK聊天页面**: http://***************:1234/final-custom-layout-demo.html

#### 友好路由
- **导航页面**: http://***************:1234/
- **自定义聊天**: http://***************:1234/chat
- **SDK聊天**: http://***************:1234/sdk 或 http://***************:1234/demo

### 页面功能说明

#### 纯自定义聊天页面 (index.html)
- 完全自定义的聊天界面
- 支持多智能体切换
- Markdown渲染支持
- 微信式聊天体验
- 适合需要高度定制化的场景

#### 自定义边栏+SDK聊天页面 (final-custom-layout-demo.html)
- 左侧自定义智能体选择边栏
- 右侧集成官方Coze SDK聊天窗口
- 兼具定制性和官方功能完整性
- 支持官方SDK的所有功能

## 部署步骤

### 1. 本地开发环境

#### 方法一：使用Python（推荐）
```bash
# 进入项目目录
cd your-project-directory

# Python 3
python -m http.server 8080

# 或 Python 2
python -m SimpleHTTPServer 8080
```

#### 方法二：使用Node.js
```bash
# 安装http-server
npm install -g http-server

# 启动服务器
http-server -p 8080
```

#### 方法三：使用PHP
```bash
php -S localhost:8080
```

### 2. Nginx部署

#### nginx.conf 配置示例
```nginx
server {
    listen 80;
    server_name localhost;
    
    # 设置根目录
    root /usr/share/nginx/html;
    index navigation.html;
    
    # 主页面路由 - 默认访问导航页面
    location = / {
        try_files /navigation.html =404;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
    }
    
    # 演示页面路由
    location = /demo {
        try_files /final-custom-layout-demo.html =404;
        add_header Access-Control-Allow-Origin *;
    }
    
    # SDK聊天页面路由
    location = /sdk {
        try_files /final-custom-layout-demo.html =404;
        add_header Access-Control-Allow-Origin *;
    }
    
    # 自定义聊天页面路由
    location = /chat {
        try_files /index.html =404;
        add_header Access-Control-Allow-Origin *;
    }
    
    # 直接访问HTML文件
    location ~ ^/(index|final-custom-layout-demo|navigation)\.html$ {
        try_files $uri =404;
        add_header Access-Control-Allow-Origin *;
    }
    
    # 处理静态文件
    location / {
        try_files $uri $uri/ =404;
        add_header Access-Control-Allow-Origin *;
    }
    
    # 处理静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin *;
    }
    
    # 处理JSON配置文件
    location ~* \.(json)$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Access-Control-Allow-Origin *;
        add_header Content-Type application/json;
    }
}
```

### 3. Docker部署

#### Dockerfile
```dockerfile
FROM nginx:alpine

# 复制项目文件
COPY . /usr/share/nginx/html/

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  chat-app:
    build: .
    container_name: ai-chat-system
    ports:
      - "1234:80"  # 映射到宿主机的1234端口
    volumes:
      - ./config.json:/usr/share/nginx/html/config.json:ro  # 只读挂载配置文件
      - ./touxiang:/usr/share/nginx/html/touxiang:ro         # 只读挂载头像目录
    restart: unless-stopped
    networks:
      - chat-network

networks:
  chat-network:
    driver: bridge
```

#### 快速部署命令
```bash
# 构建并启动容器
docker-compose up -d --build

# 查看运行状态
docker-compose ps

# 查看日志
docker-compose logs -f chat-app

# 停止服务
docker-compose down
```

## 配置管理

### 环境变量配置
可以通过环境变量配置Coze API：

```javascript
// 在main.js中添加环境变量支持
const COZE_CONFIG = {
  bot_id: process.env.COZE_BOT_ID || '7523880023146168366',
  token: process.env.COZE_TOKEN || 'your-default-token',
  api_base: process.env.COZE_API_BASE || 'https://api.coze.cn/open_api/v2',
};
```

### 配置文件优先级
1. `config.json` - 新配置文件（优先）
2. `agents-config.json` - 旧配置文件（兼容）
3. 默认配置 - 硬编码配置（备用）

## 安全注意事项

### 1. API Token安全
- 不要在前端代码中硬编码敏感的API Token
- 考虑使用后端代理来处理API调用
- 定期轮换API Token

### 2. CORS配置
```nginx
# 添加CORS头
add_header Access-Control-Allow-Origin *;
add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
add_header Access-Control-Allow-Headers "Content-Type, Authorization";
```

### 3. 文件权限
```bash
# 设置适当的文件权限
chmod 644 *.html *.js *.css *.json
chmod 755 touxiang/
```

## 监控和日志

### 1. Nginx访问日志
```nginx
access_log /var/log/nginx/chat-app-access.log;
error_log /var/log/nginx/chat-app-error.log;
```

### 2. JavaScript错误监控
```javascript
// 添加全局错误处理
window.addEventListener('error', function(e) {
    console.error('Global error:', e.error);
    // 可以发送到监控服务
});
```

## 性能优化

### 1. 静态资源缓存
```nginx
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

### 2. Gzip压缩
```nginx
gzip on;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
```

### 3. CDN配置
```html
<!-- 使用CDN加载第三方库 -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/common.min.js"></script>
```

## 故障排除

### 常见问题

1. **配置不生效**
   - 检查config.json文件格式
   - 确认文件路径正确
   - 查看浏览器控制台错误

2. **API调用失败**
   - 验证Bot ID和Token
   - 检查网络连接
   - 确认API端点可访问

3. **图片不显示**
   - 检查touxiang目录权限
   - 确认图片文件存在
   - 验证图片路径格式

### 调试工具

1. **配置测试页面**
   ```
   http://your-domain/test-config.html
   ```

2. **浏览器开发者工具**
   - Network标签：检查资源加载
   - Console标签：查看JavaScript错误
   - Application标签：检查本地存储

3. **服务器日志**
   ```bash
   tail -f /var/log/nginx/error.log
   ```

## 更新和维护

### 1. 配置备份
```bash
# 定期备份配置文件
cp config.json config.json.backup.$(date +%Y%m%d)
```

### 2. 版本控制
```bash
# 使用Git管理代码
git add .
git commit -m "Update agent configuration"
git push origin main
```

### 3. 自动部署
```bash
#!/bin/bash
# deploy.sh
git pull origin main
docker-compose down
docker-compose up -d --build
```