📁 文件结构说明
1. tv-main.html - 主页面文件
包含HTML结构和基本布局
引入所有CSS和JS模块
保持简洁，只有必要的DOM结构
2. tv-styles.css - 样式文件
所有CSS样式集中管理
响应式设计和动画效果
老年友好型界面样式
3. tv-config.js - 配置文件
Coze API配置
专家信息配置
主题与专家映射关系
4. tv-data.js - 数据文件
磁贴数据配置
主题内容和提示词
可以继续扩展其他主题数据
5. tv-sdk.js - SDK管理模块
封装Coze SDK操作
客户端创建和销毁
消息发送功能
6. tv-app.js - 主应用逻辑
应用初始化和状态管理
磁贴交互逻辑
主题切换和导航
🎯 核心功能实现
点击磁贴自动对接智能体的流程：

用户点击二级磁贴
handleSecondLevelClick() 获取预置提示词
根据当前主题找到对应的专家ID
sdkManager.createSDKClient() 创建SDK客户端
自动发送预置提示词给对应的智能体
显示聊天界面，用户可以继续对话
这样的模块化结构让代码更易维护，每个文件职责清晰，后续添加新功能或修改现有功能都会更加方便！