<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能电视鼠标操作界面 - 老年友好版</title>
    <link rel="stylesheet" href="tv-styles.css">
</head>

<body>
    <div class="main-container">
        <!-- 左栏 - 专家会客室 -->
        <div class="expert-sidebar">
            <div class="sidebar-header">
                <h2>🏠 专家会客室</h2>
                <p>点击电话图标与专家语音通话</p>
            </div>
            
            <div class="expert-list" id="expert-list">
                <!-- 专家列表将在这里动态生成 -->
            </div>
        </div>

        <!-- 右栏 - 主题交互区 -->
        <div class="theme-area">
            <div class="theme-header">
                <div class="theme-tabs" id="theme-tabs">
                    <div class="theme-tab active" data-theme="yangsheng">养生</div>
                    <div class="theme-tab" data-theme="sannong">三农</div>
                    <div class="theme-tab" data-theme="dangjian">党建</div>
                    <div class="theme-tab" data-theme="jianshen">健身</div>
                    <div class="theme-tab" data-theme="yanghua">养花</div>
                </div>
            </div>

            <div class="content-area">
                <button class="back-button" id="back-button" onclick="goBackToFirstLevel()">
                    ← 返回上一级
                </button>
                
                <div class="tile-grid" id="tile-grid">
                    <!-- 磁贴将在这里动态生成 -->
                </div>

                <!-- SDK聊天容器 -->
                <div id="coze-chat-container" style="display: none; position: absolute; top: 0; left: 0; right: 0; bottom: 0; width: 100%; height: 100%; background: white; z-index: 100;">
                    <!-- Coze SDK 聊天界面将在这里显示 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 通话界面 -->
    <div class="call-interface" id="call-interface">
        <div class="call-content">
            <img class="call-avatar calling-animation" id="call-avatar" src="" alt="专家头像">
            <div class="call-name" id="call-name">专家姓名</div>
            <div class="call-status" id="call-status">正在连接...</div>
            <div class="call-controls">
                <button class="end-call-btn" onclick="endCall()">📞</button>
            </div>
        </div>
    </div>

    <!-- 引入必要的库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/lib/common.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/highlight.js@11.9.0/styles/github.min.css" />
    <!-- 引入Coze SDK -->
    <script src="https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.15/libs/cn/index.js"></script>

    <!-- 引入模块化的JS文件 -->
    <script src="tv-config.js"></script>
    <script src="tv-data.js"></script>
    <script src="tv-sdk.js"></script>
    <script src="tv-app.js"></script>
</body>
</html>