# Project Structure

## Root Directory Layout

```
├── index.html                        # Main custom chat interface
├── navigation.html                   # Navigation/landing page
├── final-custom-layout-demo.html     # Custom sidebar + SDK chat
├── final-demo-config-manager.html    # Configuration manager demo
├── config.html                       # Configuration interface
├── test-config.html                  # Configuration testing page
├── tv.html                          # TV display interface
├── main.js                          # Core JavaScript logic
├── style.css                        # Main stylesheet
├── config.json                      # Agent configuration (primary)
├── agents-config.json               # Legacy configuration (fallback)
├── nginx.conf                       # Nginx server configuration
├── Dockerfile                       # Docker build configuration
├── docker-compose.yml               # Docker compose setup
├── touxiang/                        # Avatar images directory
├── 部署说明.md                       # Deployment documentation (Chinese)
└── README.md                        # Project documentation
```

## Key File Purposes

### Core Application Files
- **index.html**: Primary chat interface with custom UI
- **main.js**: Contains all chat logic, API integration, and UI management
- **style.css**: WeChat-style UI components and responsive design

### Configuration Files
- **config.json**: Modern agent configuration with categories and metadata
- **agents-config.json**: Legacy format for backward compatibility
- **test-config.html**: Tool for testing configuration changes

### Deployment Files
- **nginx.conf**: Production web server configuration with routing
- **Dockerfile**: Container build instructions
- **docker-compose.yml**: Multi-container orchestration

### Assets
- **touxiang/**: Avatar images for agents and users (PNG format)

## Code Organization Patterns

### JavaScript Structure (main.js)
- API configuration constants at top
- Utility functions (markdown processing, URL handling)
- Agent management functions
- Chat functionality (send/receive messages)
- UI management and event handlers
- Initialization code at bottom

### CSS Architecture (style.css)
- CSS custom properties for theming
- Component-based styling (chat-container, agent-sidebar, etc.)
- Responsive design patterns
- Animation and transition definitions

### Configuration Schema
```json
{
  "agents": [
    {
      "id": "unique_id",
      "name": "Display Name",
      "description": "Brief description",
      "category": "category_id",
      "bot_id": "coze_bot_id",
      "greeting": "Welcome message",
      "avatar": "touxiang/image.png"
    }
  ],
  "categories": [
    {
      "id": "category_id",
      "name": "Category Name"
    }
  ]
}
```

## Development Conventions

- Use semantic HTML5 elements
- Follow BEM-like CSS naming for components
- Keep JavaScript functions focused and modular
- Store all configuration in JSON files, not hardcoded
- Use async/await for API calls
- Implement proper error handling for network requests